/**
 * TMDB API Service
 * 
 * Comprehensive service for interacting with The Movie Database API
 * Handles movies, TV series, cast, crew, and image fetching
 */

// TMDB API Configuration
const TMDB_API_KEY = import.meta.env.VITE_TMDB_API_KEY || '';
const TMDB_BASE_URL = import.meta.env.VITE_TMDB_BASE_URL || 'https://api.themoviedb.org/3';

// Enhanced TMDB ID validation
export function isValidTMDBId(id: string | number): boolean {
  const numericId = typeof id === 'string' ? parseInt(id, 10) : id;
  return !isNaN(numericId) && numericId > 0 && numericId < 10000000; // Reasonable range for TMDB IDs
}
const TMDB_IMAGE_BASE_URL = import.meta.env.VITE_TMDB_IMAGE_BASE_URL || 'https://image.tmdb.org/t/p';

// Rate limiting configuration
const RATE_LIMIT_DELAY = 250; // 4 requests per second (TMDB limit is 40/10s)
let lastRequestTime = 0;

// Image size configurations
export const IMAGE_SIZES = {
  poster: {
    small: 'w185',
    medium: 'w342',
    large: 'w500',
    original: 'original'
  },
  backdrop: {
    small: 'w300',
    medium: 'w780',
    large: 'w1280',
    original: 'original'
  },
  profile: {
    small: 'w45',
    medium: 'w185',
    large: 'h632',
    original: 'original'
  }
};

// TMDB API Response Interfaces
export interface TMDBMovie {
  id: number;
  title: string;
  original_title: string;
  overview: string;
  release_date: string;
  poster_path: string | null;
  backdrop_path: string | null;
  genre_ids: number[];
  genres?: TMDBGenre[];
  adult: boolean;
  original_language: string;
  popularity: number;
  vote_average: number;
  vote_count: number;
  runtime?: number;
  production_companies?: TMDBProductionCompany[];
  production_countries?: TMDBProductionCountry[];
  spoken_languages?: TMDBSpokenLanguage[];
  status?: string;
  tagline?: string;
  budget?: number;
  revenue?: number;
  imdb_id?: string;
  homepage?: string;
  videos?: {
    results: TMDBVideo[];
  };
  credits?: TMDBCredits;
}

export interface TMDBTVShow {
  id: number;
  name: string;
  original_name: string;
  overview: string;
  first_air_date: string;
  last_air_date?: string;
  poster_path: string | null;
  backdrop_path: string | null;
  genre_ids: number[];
  genres?: TMDBGenre[];
  adult: boolean;
  original_language: string;
  popularity: number;
  vote_average: number;
  vote_count: number;
  number_of_episodes?: number;
  number_of_seasons?: number;
  episode_run_time?: number[];
  production_companies?: TMDBProductionCompany[];
  production_countries?: TMDBProductionCountry[];
  spoken_languages?: TMDBSpokenLanguage[];
  status?: string;
  tagline?: string;
  homepage?: string;
  in_production?: boolean;
  type?: string;
  networks?: TMDBNetwork[];
  seasons?: TMDBSeason[];
  videos?: {
    results: TMDBVideo[];
  };
  credits?: TMDBCredits;
}

export interface TMDBGenre {
  id: number;
  name: string;
}

export interface TMDBProductionCompany {
  id: number;
  name: string;
  logo_path: string | null;
  origin_country: string;
}

export interface TMDBProductionCountry {
  iso_3166_1: string;
  name: string;
}

export interface TMDBSpokenLanguage {
  english_name: string;
  iso_639_1: string;
  name: string;
}

export interface TMDBNetwork {
  id: number;
  name: string;
  logo_path: string | null;
  origin_country: string;
}

export interface TMDBSeason {
  id: number;
  season_number: number;
  name: string;
  overview: string;
  poster_path: string | null;
  air_date: string;
  episode_count: number;
}

export interface TMDBVideo {
  id: string;
  key: string;
  name: string;
  site: string;
  type: string;
  official: boolean;
  published_at: string;
}

export interface TMDBCredits {
  cast: TMDBCastMember[];
  crew: TMDBCrewMember[];
}

export interface TMDBCastMember {
  id: number;
  name: string;
  character: string;
  profile_path: string | null;
  order: number;
  adult: boolean;
  gender: number;
  known_for_department: string;
  original_name: string;
  popularity: number;
  cast_id: number;
  credit_id: string;
}

export interface TMDBCrewMember {
  id: number;
  name: string;
  job: string;
  department: string;
  profile_path: string | null;
  adult: boolean;
  gender: number;
  known_for_department: string;
  original_name: string;
  popularity: number;
  credit_id: string;
}

// Error types
export class TMDBError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public originalError?: any
  ) {
    super(message);
    this.name = 'TMDBError';
  }
}

// Rate limiting helper
async function rateLimitedRequest(): Promise<void> {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;
  
  if (timeSinceLastRequest < RATE_LIMIT_DELAY) {
    await new Promise(resolve => 
      setTimeout(resolve, RATE_LIMIT_DELAY - timeSinceLastRequest)
    );
  }
  
  lastRequestTime = Date.now();
}

// Generic API request function with error handling
async function tmdbRequest<T>(endpoint: string, params: Record<string, string> = {}): Promise<T> {
  if (!TMDB_API_KEY) {
    throw new TMDBError('TMDB API key is not configured');
  }

  await rateLimitedRequest();

  const url = new URL(`${TMDB_BASE_URL}${endpoint}`);
  url.searchParams.append('api_key', TMDB_API_KEY);
  
  // Add additional parameters
  Object.entries(params).forEach(([key, value]) => {
    url.searchParams.append(key, value);
  });

  try {
    const response = await fetch(url.toString());
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new TMDBError(
        errorData.status_message || `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        errorData
      );
    }

    return await response.json();
  } catch (error) {
    if (error instanceof TMDBError) {
      throw error;
    }
    
    throw new TMDBError(
      'Network error or invalid response from TMDB API',
      undefined,
      error
    );
  }
}

// Image URL builder
export function buildImageUrl(
  path: string | null, 
  type: 'poster' | 'backdrop' | 'profile' = 'poster',
  size: string = 'medium'
): string | null {
  if (!path) return null;
  
  const sizeMap = IMAGE_SIZES[type];
  const selectedSize = sizeMap[size as keyof typeof sizeMap] || sizeMap.medium;
  
  return `${TMDB_IMAGE_BASE_URL}/${selectedSize}${path}`;
}

// Get movie details by TMDB ID
export async function getMovieDetails(tmdbId: string | number): Promise<TMDBMovie> {
  return tmdbRequest<TMDBMovie>(`/movie/${tmdbId}`, {
    append_to_response: 'credits,videos,images'
  });
}

// Get TV show details by TMDB ID
export async function getTVShowDetails(tmdbId: string | number): Promise<TMDBTVShow> {
  return tmdbRequest<TMDBTVShow>(`/tv/${tmdbId}`, {
    append_to_response: 'credits,videos,images,seasons'
  });
}

// Get movie or TV show details (enhanced auto-detection)
export async function getContentDetails(tmdbId: string | number, contentType?: 'movie' | 'tv' | 'series'): Promise<TMDBMovie | TMDBTVShow> {
  // If content type is specified, use it directly
  if (contentType === 'movie') {
    return getMovieDetails(tmdbId);
  } else if (contentType === 'tv' || contentType === 'series') {
    return getTVShowDetails(tmdbId);
  }
  
  // Enhanced auto-detection: Try both simultaneously and return the successful one
  const moviePromise = getMovieDetails(tmdbId).catch(error => ({ error, type: 'movie' }));
  const tvPromise = getTVShowDetails(tmdbId).catch(error => ({ error, type: 'tv' }));
  
  try {
    const [movieResult, tvResult] = await Promise.all([moviePromise, tvPromise]);
    
    // Return the successful result
    if (movieResult && !('error' in movieResult)) {
      return movieResult;
    }
    if (tvResult && !('error' in tvResult)) {
      return tvResult;
    }
    
    // If both failed, throw the most relevant error
    const movieError = 'error' in movieResult ? movieResult.error : null;
    const tvError = 'error' in tvResult ? tvResult.error : null;
    
    if (movieError instanceof TMDBError && movieError.statusCode === 404 && 
        tvError instanceof TMDBError && tvError.statusCode === 404) {
      throw new TMDBError(`Content with ID ${tmdbId} not found in TMDB database`);
    }
    
    // Throw the first non-404 error, or movie error as fallback
    throw movieError || tvError || new TMDBError('Content not found in TMDB database');
    
  } catch (error) {
    if (error instanceof TMDBError) {
      throw error;
    }
    throw new TMDBError('Failed to fetch content from TMDB');
  }
}

// Get genre list
export async function getGenres(type: 'movie' | 'tv' = 'movie'): Promise<TMDBGenre[]> {
  const response = await tmdbRequest<{ genres: TMDBGenre[] }>(`/genre/${type}/list`);
  return response.genres;
}

// Search for content
export async function searchContent(query: string, type?: 'movie' | 'tv'): Promise<{
  results: (TMDBMovie | TMDBTVShow)[];
  total_results: number;
  total_pages: number;
}> {
  const endpoint = type ? `/search/${type}` : '/search/multi';
  return tmdbRequest(endpoint, { query });
}

// Data transformation utilities
export function transformTMDBToMediaItem(tmdbData: TMDBMovie | TMDBTVShow): {
  title: string;
  description: string;
  year: string;
  genres: string[];
  posterUrl: string;
  thumbnailUrl: string;
  coverImage: string;
  imdbRating: string;
  runtime: string;
  studio: string;
  languages: string[];
  trailer: string;
  cast: string[];
  crew: string[];
  director: string;
  writers: string[];
  producers: string[];
} {
  const isMovie = 'title' in tmdbData;

  // Extract basic information
  const title = isMovie ? tmdbData.title : tmdbData.name;
  const description = tmdbData.overview || '';
  const releaseDate = isMovie ? tmdbData.release_date : tmdbData.first_air_date;
  const year = releaseDate ? new Date(releaseDate).getFullYear().toString() : '';

  // Extract genres
  const genres = tmdbData.genres?.map(g => g.name) || [];

  // Build image URLs
  const posterUrl = buildImageUrl(tmdbData.poster_path, 'poster', 'large') || '';
  const thumbnailUrl = buildImageUrl(tmdbData.poster_path, 'poster', 'medium') || '';
  const coverImage = buildImageUrl(tmdbData.backdrop_path, 'backdrop', 'original') || '';

  // Extract rating and runtime
  const imdbRating = tmdbData.vote_average ? tmdbData.vote_average.toFixed(1) : '';
  let runtime = '';
  if (isMovie && tmdbData.runtime) {
    runtime = tmdbData.runtime.toString();
  } else if (!isMovie && tmdbData.episode_run_time?.length) {
    runtime = tmdbData.episode_run_time[0].toString();
  }

  // Extract studio/production companies
  const studio = tmdbData.production_companies?.[0]?.name || '';

  // Extract languages
  const languages = tmdbData.spoken_languages?.map(lang => lang.english_name) || [];

  // Extract trailer
  const youtubeTrailer = tmdbData.videos?.results.find(
    video => video.site === 'YouTube' && video.type === 'Trailer'
  );
  const trailer = youtubeTrailer ? `https://www.youtube.com/watch?v=${youtubeTrailer.key}` : '';

  // Extract cast and crew information
  const cast = tmdbData.credits?.cast.slice(0, 10).map(member => member.name) || [];
  const crew = tmdbData.credits?.crew.slice(0, 5).map(member => `${member.name} (${member.job})`) || [];

  // Extract specific crew roles
  const director = tmdbData.credits?.crew.find(member => member.job === 'Director')?.name || '';
  const writers = tmdbData.credits?.crew
    .filter(member => ['Writer', 'Screenplay', 'Story'].includes(member.job))
    .map(member => member.name) || [];
  const producers = tmdbData.credits?.crew
    .filter(member => member.job.includes('Producer'))
    .map(member => member.name) || [];

  return {
    title,
    description,
    year,
    genres,
    posterUrl,
    thumbnailUrl,
    coverImage,
    imdbRating,
    runtime,
    studio,
    languages,
    trailer,
    cast,
    crew,
    director,
    writers,
    producers
  };
}

// Get comprehensive content data with all metadata
export async function getComprehensiveContentData(tmdbId: string | number, contentType?: 'movie' | 'tv') {
  try {
    const tmdbData = await getContentDetails(tmdbId, contentType);
    const transformedData = transformTMDBToMediaItem(tmdbData);

    return {
      success: true,
      data: transformedData,
      rawData: tmdbData,
      contentType: 'title' in tmdbData ? 'movie' : 'series'
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof TMDBError ? error.message : 'Unknown error occurred',
      statusCode: error instanceof TMDBError ? error.statusCode : undefined
    };
  }
}

// Duplicate function removed - using enhanced version at top of file

// Get multiple image sizes for a poster
export function getPosterUrls(posterPath: string | null) {
  if (!posterPath) return null;

  return {
    small: buildImageUrl(posterPath, 'poster', 'small'),
    medium: buildImageUrl(posterPath, 'poster', 'medium'),
    large: buildImageUrl(posterPath, 'poster', 'large'),
    original: buildImageUrl(posterPath, 'poster', 'original')
  };
}

// Get multiple image sizes for a backdrop
export function getBackdropUrls(backdropPath: string | null) {
  if (!backdropPath) return null;

  return {
    small: buildImageUrl(backdropPath, 'backdrop', 'small'),
    medium: buildImageUrl(backdropPath, 'backdrop', 'medium'),
    large: buildImageUrl(backdropPath, 'backdrop', 'large'),
    original: buildImageUrl(backdropPath, 'backdrop', 'original')
  };
}
